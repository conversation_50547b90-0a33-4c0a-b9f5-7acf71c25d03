// USER CHART SHEET exported by JRC ECDIS.
// <<NOTE>>This strings // indicate comment column/cells. You can edit freely.
// Test Onl,,
// SYMBOL,InstName
// Comment
// Lat,,,Lon
SYMBOL,~WARNSY6,***,***
Warning Comment
17,48.000,N,055,56.000,W
// TEXT
// Comment
// Lat,,,Lon,Rotation
TEXT,Warning Text
Warning Text Comment
17,48.000,N,055,56.000,W,0,12,
// POLYGON
// Comment
// Lat,,,Lon,Add "END" to the end of vertex.
POLYGON
Polygon Comment
17,45.000,N,055,58.000,W
17,45.000,N,055,48.000,W
17,40.000,N,055,48.000,W
17,40.000,N,055,58.000,W
17,45.000,N,055,58.000,W
END
// TEXT
// Comment
// Lat,,,Lon,Rotation
TEXT,Polygon Text
Polygon Comment
17,45.000,N,055,58.000,W,0,12,
// LINE,InstName
// Comment
// Lat,,,Lon,Add "END" to the end of vertex.
LINE,***
Simple Line Comment
17,45.000,N,056,05.000,W
17,54.520,N,055,54.614,W
17,56.813,N,055,51.021,W
17,57.132,N,055,43.901,W
17,52.354,N,055,40.507,W
END
// TEXT
// Comment
// Lat,,,Lon,Rotation
TEXT,Simple Text Text
Simple Text Comment
17,45.000,N,056,05.000,W,0,12,
// SYMBOL,InstName
// Comment
// Lat,,,Lon
SYMBOL,~SQUARE6,***,***
SQUARE
17,49.327,N,055,44.610,W
// SYMBOL,InstName
// Comment
// Lat,,,Lon
SYMBOL,~DIAMND6,***,***
DIAMOND
17,46.775,N,055,42.844,W
// CIRCLE
// Comment
// Base Point-Lat,,,Base Point-Lon,,,Radius[nm]
CIRCLE
CIRCLE AREA
17,40.614,N,055,42.278,W,2.6
// FAN
// Comment
// Base Point-Lat,,,Base Point-Lon,,,Radius[nm],Start Angle[deg],End Angle[deg]
FAN
FAN
17,33.173,N,055,56.067,W,4.51,64.0,106.2
// ARC
// Comment
// Base Point-Lat,,,Base Point-Lon,,,Radius[nm],Start Angle[deg],End Angle[deg]
ARC

17,33.364,N,056,05.126,W,5.66,28.3,65.1
// DANGER_LINE
// Comment
// Lat,,,Lon,Add "END" to the end of vertex.
DANGER_LINE
WARNING LINE
17,31.959,N,055,47.274,W
17,33.460,N,055,45.376,W
17,34.163,N,055,42.445,W
17,33.524,N,055,38.848,W
END
// DANGER_AREA
// Comment
// Lat,,,Lon,Add "END" to the end of vertex.
DANGER_AREA
WARNING AREA
17,43.968,N,055,35.619,W
17,42.819,N,055,30.792,W
17,38.991,N,055,32.357,W
17,38.895,N,055,35.619,W
17,44.063,N,055,35.685,W
17,43.968,N,055,35.619,W
END
// SYMBOL,InstName
// Comment
// Lat,,,Lon
SYMBOL,~XSHAPE6,***,***

17,27.248,N,055,55.743,W
// SYMBOL,InstName
// Comment
// Lat,,,Lon
SYMBOL,~TRIANG6,***,***

17,48.635,N,055,36.634,W
// SYMBOL,InstName
// Comment
// Lat,,,Lon
SYMBOL,~CIRCLE6,***,***

17,49.910,N,055,41.095,W
