<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JRC ECDIS User Map Editor</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
        }

        .main-container {
            height: 100vh;
            overflow: hidden;
        }

        .left-panel {
            background: white;
            border-right: 2px solid #e9ecef;
            height: 100vh;
            overflow-y: auto;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
        }

        .right-panel {
            background: #f8f9fa;
            height: 100vh;
            overflow-y: auto;
            padding: 0;
        }

        .app-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            margin: 0;
        }

        .app-title {
            margin: 0;
            font-size: 1.6rem;
            font-weight: 600;
        }

        .add-entry-section {
            background: white;
            padding: 25px;
            border-bottom: 1px solid #e9ecef;
            flex: 1;
        }

        .section-title {
            color: #495057;
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }

        .export-section {
            background: white;
            padding: 25px;
            border-top: 1px solid #e9ecef;
            margin-top: auto;
        }

        .entries-header {
            background: white;
            padding: 25px;
            border-bottom: 2px solid #e9ecef;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .entries-container {
            padding: 20px;
            height: calc(100vh - 120px);
            overflow-y: auto;
        }

        .entry-card {
            background: white;
            border: 1px solid #e9ecef;
            border-left: 4px solid #667eea;
            border-radius: 8px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .entry-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }

        .entry-type {
            font-weight: 600;
            color: #667eea;
            font-size: 1.1rem;
        }

        .entry-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            border-radius: 8px 8px 0 0;
        }

        .entry-body {
            padding: 20px;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .btn-custom {
            border-radius: 6px;
            font-weight: 500;
            padding: 8px 16px;
            transition: all 0.3s ease;
            border: none;
        }

        .btn-primary-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary-custom:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .btn-success-custom {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }

        .btn-info-custom {
            background: linear-gradient(135deg, #3498db 0%, #85c1e9 100%);
            color: white;
        }

        .btn-warning-custom {
            background: linear-gradient(135deg, #f39c12 0%, #f7dc6f 100%);
            color: white;
        }

        .edit-mode {
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }

        .edit-form {
            margin-top: 15px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .form-control, .form-select {
            border-radius: 6px;
            border: 1px solid #ced4da;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .small {
            font-size: 0.875rem;
            margin-bottom: 2px;
            margin-right: 5px;
            font-weight: 600;
            color: #495057;
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }

        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            margin: 0;
        }

        .stats-label {
            font-size: 0.9rem;
            opacity: 0.9;
            margin: 0;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state-text {
            font-size: 1.1rem;
            margin-bottom: 10px;
        }

        .scrollbar-custom::-webkit-scrollbar {
            width: 8px;
        }

        .scrollbar-custom::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .scrollbar-custom::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .scrollbar-custom::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>
</head>
<body>
    <div class="container-fluid main-container">
        <div class="row h-100">
            <!-- Left Panel -->
            <div class="col-md-5 left-panel">
                <!-- Header -->
                <div class="app-header">
                    <h1 class="app-title">JRC ECDIS User Map Editor</h1>
                </div>

                <!-- Add New Entry Section -->
                <div class="add-entry-section">
                    <h3 class="section-title">Add New Entry</h3>
                    <div class="mb-3">
                        <label for="entryType" class="form-label">Entry Type</label>
                        <select id="entryType" class="form-select" onchange="updateEntryForm()">
                            <option value="">Select Entry Type</option>
                            <option value="SYMBOL">SYMBOL</option>
                            <option value="TEXT">TEXT</option>
                            <option value="POLYGON">POLYGON</option>
                            <option value="LINE">LINE</option>
                            <option value="CIRCLE">CIRCLE</option>
                            <option value="ELLIPSE">ELLIPSE</option>
                            <option value="FAN">FAN</option>
                            <option value="ARC">ARC</option>
                            <option value="DANGER_LINE">DANGER_LINE</option>
                            <option value="DANGER_AREA">DANGER_AREA</option>
                        </select>
                    </div>
                    <div id="entryForm"></div>
                    <button id="addEntryBtn" class="btn btn-primary-custom btn-custom w-100 mt-3" onclick="addEntry()" disabled>
                        Add Entry
                    </button>
                </div>

                <!-- Export Section -->
                <div class="export-section">
                    <div class="stats-card">
                        <p class="stats-number" id="entryCount">0</p>
                        <p class="stats-label">Total Entries</p>
                    </div>

                    <h4 class="section-title">Actions</h4>
                    <button class="btn btn-success-custom btn-custom w-100 mb-3" onclick="exportData()">
                        Export to CSV
                    </button>
                    <button class="btn btn-info-custom btn-custom w-100 mb-3" onclick="loadSampleData()">
                        Load Sample Data
                    </button>
                    <button class="btn btn-warning-custom btn-custom w-100 mb-3" onclick="clearAllData()">
                        Clear All Data
                    </button>
                    <div class="mt-3">
                        <label for="fileInput" class="form-label">Import File</label>
                        <input type="file" id="fileInput" class="form-control" accept=".csv,.txt" onchange="importFile(event)">
                        <small class="text-muted">Supports CSV and ECDIS text format</small>
                    </div>
                </div>
            </div>

            <!-- Right Panel -->
            <div class="col-md-7 right-panel">
                <!-- Entries Header -->
                <div class="entries-header">
                    <h3 class="section-title mb-0">Current Entries</h3>
                </div>

                <!-- Entries Container -->
                <div class="entries-container scrollbar-custom" id="entriesContainer">
                    <div class="empty-state" id="emptyState">
                        <div class="empty-state-text">No entries yet</div>
                        <small class="text-muted">Add your first ECDIS entry using the form on the left</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="app.js"></script>
</body>
</html>
