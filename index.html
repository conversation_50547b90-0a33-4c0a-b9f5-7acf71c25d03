<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JRC ECDIS User Map Editor</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .entry-card {
            border-left: 4px solid #007bff;
            margin-bottom: 1rem;
        }
        .entry-type {
            font-weight: bold;
            color: #007bff;
        }

        .add-entry-section {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .entry-header {
            display: flex;
            justify-content: between;
            align-items: center;
        }
        .delete-btn {
            opacity: 0.7;
        }
        .delete-btn:hover {
            opacity: 1;
        }

        .export-section {
            position: sticky;
            top: 20px;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
        }
        .edit-mode {
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }
        .edit-form {
            margin-top: 10px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .action-buttons {
            display: flex;
            gap: 5px;
        }

        .small {
            font-size: 0.875rem;
            margin-bottom: 2px;
            margin-right: 5px;
            font-weight: 600;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-md-8">
                <h1 class="mb-4">
                    JRC ECDIS User Map Editor
                </h1>
                
                <!-- Add New Entry Section -->
                <div class="add-entry-section">
                    <h3>Add New Entry</h3>
                    <div class="row">
                        <div class="col-md-4">
                            <label for="entryType" class="form-label">Entry Type</label>
                            <select id="entryType" class="form-select" onchange="updateEntryForm()">
                                <option value="">Select Entry Type</option>
                                <option value="SYMBOL">SYMBOL</option>
                                <option value="TEXT">TEXT</option>
                                <option value="POLYGON">POLYGON</option>
                                <option value="LINE">LINE</option>
                                <option value="CIRCLE">CIRCLE</option>
                                <option value="ELLIPSE">ELLIPSE</option>
                                <option value="FAN">FAN</option>
                                <option value="ARC">ARC</option>
                                <option value="DANGER_LINE">DANGER_LINE</option>
                                <option value="DANGER_AREA">DANGER_AREA</option>
                            </select>
                        </div>
                        <div class="col-md-8">
                            <div id="entryForm"></div>
                        </div>
                    </div>
                    <button id="addEntryBtn" class="btn btn-primary mt-3" onclick="addEntry()" disabled>
                        Add Entry
                    </button>
                </div>

                <!-- Entries List -->
                <div id="entriesList">
                    <h3>Current Entries</h3>
                    <div id="entriesContainer"></div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="export-section">
                    <h4>Export</h4>
                    <button class="btn btn-success w-100 mb-3" onclick="exportData()">
                        Export to CSV
                    </button>
                    <button class="btn btn-info w-100 mb-3" onclick="loadSampleData()">
                        Load Sample Data
                    </button>
                    <button class="btn btn-warning w-100 mb-3" onclick="clearAllData()">
                        Clear All Data
                    </button>
                    <div class="mt-3">
                        <label for="fileInput" class="form-label">Import CSV/TXT File</label>
                        <input type="file" id="fileInput" class="form-control" accept=".csv,.txt" onchange="importFile(event)">
                        <small class="text-muted">Supports both CSV and original ECDIS text format</small>
                    </div>
                    <div class="mt-3">
                        <small class="text-muted">
                            <strong>Total Entries:</strong> <span id="entryCount">0</span>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="app.js"></script>
</body>
</html>
