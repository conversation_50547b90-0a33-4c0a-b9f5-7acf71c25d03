// Global data storage
let entries = [];
let entryIdCounter = 0;

// Validation functions
function validateCoordinates(input) {
    const errors = [];
    if (!input || input.trim() === '') return { isValid: true, errors: [], coordinates: [] };

    const coordinates = [];
    const coordStrings = input.split(',').map(s => s.trim());

    coordStrings.forEach((coordStr, index) => {
        // Match pattern: "29-20.174N 094-36.404W"
        const match = coordStr.match(/(\d{1,2})-(\d{1,2}\.\d{1,3})([NS])\s+(\d{1,3})-(\d{1,2}\.\d{1,3})([EW])/);
        if (!match) {
            errors.push(`Coordinate ${index + 1}: Invalid format. Use DD-MM.MMMD DDD-MM.MMMD (e.g., 29-20.174N 094-36.404W)`);
            return;
        }

        const [, latDeg, latMin, latDir, lonDeg, lonMin, lonDir] = match;

        // Validate latitude range
        const latDegNum = parseInt(latDeg);
        const latMinNum = parseFloat(latMin);
        if (latDegNum < 0 || latDegNum > 90) {
            errors.push(`Coordinate ${index + 1}: Latitude degrees must be 0-90, got ${latDegNum}`);
        }
        if (latMinNum < 0 || latMinNum >= 60) {
            errors.push(`Coordinate ${index + 1}: Latitude minutes must be 0-59.999, got ${latMinNum}`);
        }

        // Validate longitude range
        const lonDegNum = parseInt(lonDeg);
        const lonMinNum = parseFloat(lonMin);
        if (lonDegNum < 0 || lonDegNum > 180) {
            errors.push(`Coordinate ${index + 1}: Longitude degrees must be 0-180, got ${lonDegNum}`);
        }
        if (lonMinNum < 0 || lonMinNum >= 60) {
            errors.push(`Coordinate ${index + 1}: Longitude minutes must be 0-59.999, got ${lonMinNum}`);
        }

        // Validate direction consistency
        if ((latDir === 'N' || latDir === 'S') && (lonDir === 'E' || lonDir === 'W')) {
            coordinates.push({
                lat: formatCoordinateString(latDeg, latMin),
                latDir: latDir,
                lon: formatCoordinateString(lonDeg, lonMin),
                lonDir: lonDir
            });
        } else {
            errors.push(`Coordinate ${index + 1}: Invalid direction combination. Use N/S for latitude, E/W for longitude`);
        }
    });

    return {
        isValid: errors.length === 0,
        errors: errors,
        coordinates: coordinates
    };
}

function validateText(text, fieldName) {
    if (!text) return { isValid: true, error: null };
    if (text.length > 63) {
        return {
            isValid: false,
            error: `${fieldName} must be 63 characters or less (currently ${text.length} characters)`
        };
    }
    return { isValid: true, error: null };
}

function validateFontSize(size) {
    if (!size && size !== 0) return { isValid: true, error: null };
    const sizeNum = parseInt(size);
    if (sizeNum < 6 || sizeNum > 72) {
        return {
            isValid: false,
            error: `Font size must be between 6 and 72, got ${sizeNum}`
        };
    }
    return { isValid: true, error: null };
}

// Coordinate parsing and formatting functions
function parseUserCoordinateInput(input) {
    // This function now just calls validateCoordinates and returns coordinates if valid
    const validation = validateCoordinates(input);
    return validation.isValid ? validation.coordinates : [];
}

function formatCoordinateForDisplay(coord) {
    // Convert "29,20.174" format to "29-20.174"
    if (!coord) return '';
    return coord.replace(',', '-');
}

function formatCoordinateForUser(coordinates) {
    // Convert internal format to user input format: "29-20.174N 094-36.404W"
    if (!coordinates || coordinates.length === 0) return '';

    return coordinates.map(coord => {
        const lat = formatCoordinateForDisplay(coord.lat);
        const lon = formatCoordinateForDisplay(coord.lon);
        return `${lat}${coord.latDir} ${lon}${coord.lonDir}`;
    }).join(', ');
}

function formatCoordinateString(degrees, minutes) {
    // Format into "29,20.174" format (internal storage)
    if (!degrees && !minutes) return '';
    const formattedDeg = degrees ? String(degrees).padStart(degrees.toString().length <= 2 ? 2 : 3, '0') : '00';
    const formattedMin = minutes ? parseFloat(minutes).toFixed(3).padStart(6, '0') : '00.000';
    return `${formattedDeg},${formattedMin}`;
}

// Symbol types available
const symbolTypes = {
    '~TRIANG6': 'Triangle',
    '~CIRCLE6': 'Circle',
    '~SQUARE6': 'Square',
    '~DIAMND6': 'Diamond',
    '~XSHAPE6': 'X-Shape',
    '~WARNSY6': 'Warning'
};

// Entry type definitions with their required fields
const entryTypes = {
    SYMBOL: {
        fields: ['instName', 'comment', 'lat', 'latDir', 'lon', 'lonDir'],
        template: {
            instName: '~WARNSY6',
            comment: '',
            coordinates: [{ lat: '', latDir: 'N', lon: '', lonDir: 'W' }]
        }
    },
    TEXT: {
        fields: ['text', 'comment', 'lat', 'latDir', 'lon', 'lonDir', 'rotation', 'size'],
        template: {
            text: '',
            comment: '',
            coordinates: [{ lat: '', latDir: 'N', lon: '', lonDir: 'W' }],
            rotation: 0,
            size: 11
        }
    },
    POLYGON: {
        fields: ['comment', 'coordinates'],
        template: {
            comment: '',
            coordinates: [
                { lat: '', latDir: 'N', lon: '', lonDir: 'W' }
            ]
        }
    },
    LINE: {
        fields: ['instName', 'comment', 'coordinates'],
        template: {
            instName: '***',
            comment: '',
            coordinates: [
                { lat: '', latDir: 'N', lon: '', lonDir: 'W' }
            ]
        }
    },
    CIRCLE: {
        fields: ['comment', 'lat', 'latDir', 'lon', 'lonDir', 'radius'],
        template: {
            comment: '',
            coordinates: [{ lat: '', latDir: 'N', lon: '', lonDir: 'W' }],
            radius: 0.1
        }
    },
    ELLIPSE: {
        fields: ['comment', 'lat', 'latDir', 'lon', 'lonDir', 'horizontalRadius', 'verticalRadius'],
        template: {
            comment: '',
            coordinates: [{ lat: '', latDir: 'N', lon: '', lonDir: 'W' }],
            horizontalRadius: 0.14,
            verticalRadius: 0.02
        }
    },
    FAN: {
        fields: ['comment', 'lat', 'latDir', 'lon', 'lonDir', 'radius', 'startAngle', 'endAngle'],
        template: {
            comment: '',
            coordinates: [{ lat: '', latDir: 'N', lon: '', lonDir: 'W' }],
            radius: 0.25,
            startAngle: 19.8,
            endAngle: 339.5
        }
    },
    ARC: {
        fields: ['comment', 'lat', 'latDir', 'lon', 'lonDir', 'radius', 'startAngle', 'endAngle'],
        template: {
            comment: '',
            coordinates: [{ lat: '', latDir: 'N', lon: '', lonDir: 'W' }],
            radius: 0.33,
            startAngle: 76.9,
            endAngle: 101.6
        }
    },
    DANGER_LINE: {
        fields: ['comment', 'coordinates'],
        template: {
            comment: '',
            coordinates: [
                { lat: '', latDir: 'N', lon: '', lonDir: 'W' }
            ]
        }
    },
    DANGER_AREA: {
        fields: ['comment', 'coordinates'],
        template: {
            comment: '',
            coordinates: [
                { lat: '', latDir: 'N', lon: '', lonDir: 'W' }
            ]
        }
    }
};

// Update entry form based on selected type
function updateEntryForm() {
    const entryType = document.getElementById('entryType').value;
    const formContainer = document.getElementById('entryForm');
    const addBtn = document.getElementById('addEntryBtn');
    
    if (!entryType) {
        formContainer.innerHTML = '';
        addBtn.disabled = true;
        return;
    }
    
    addBtn.disabled = false;
    const template = entryTypes[entryType].template;
    
    let formHTML = '<div class="row">';
    
    // Add specific fields based on entry type
    if (template.instName !== undefined) {
        if (entryType === 'SYMBOL') {
            // Create symbol dropdown
            formHTML += `
                <div class="col-md-6 mb-2">
                    <label class="form-label">Symbol Type</label>
                    <select id="instName" class="form-select">`;
            Object.entries(symbolTypes).forEach(([value, label]) => {
                const selected = value === template.instName ? 'selected' : '';
                formHTML += `<option value="${value}" ${selected}>${label} (${value})</option>`;
            });
            formHTML += `
                    </select>
                </div>`;
        } else {
            formHTML += `
                <div class="col-md-6 mb-2">
                    <label class="form-label">Instance Name</label>
                    <input type="text" id="instName" class="form-control" value="${template.instName}">
                </div>`;
        }
    }
    
    if (template.text !== undefined) {
        formHTML += `
            <div class="col-md-6 mb-2">
                <label class="form-label">Text</label>
                <input type="text" id="text" class="form-control" placeholder="Enter text" maxlength="63">
                <small class="text-muted">Max 63 characters</small>
            </div>`;
    }

    formHTML += `
        <div class="col-md-12 mb-2">
            <label class="form-label">Comment</label>
            <input type="text" id="comment" class="form-control" placeholder="Enter comment" maxlength="63">
            <small class="text-muted">Max 63 characters</small>
        </div>`;
    
    // Add coordinate fields
    if (entryType === 'POLYGON' || entryType === 'LINE' || entryType === 'DANGER_LINE' || entryType === 'DANGER_AREA') {
        formHTML += `
            <div class="col-md-12 mb-2">
                <label class="form-label">Coordinates (Multiple)</label>
                <textarea id="coordinates" class="form-control" rows="3"
                    placeholder="Enter coordinates separated by commas:&#10;29-20.174N 094-36.404W, 29-20.055N 094-35.470W, 29-19.878N 094-35.424W"></textarea>
                <small class="text-muted">Format: DD-MM.MMMD DDD-MM.MMMD (e.g., 29-20.174N 094-36.404W)</small>
            </div>`;
    } else {
        formHTML += `
            <div class="col-md-12 mb-2">
                <label class="form-label">Coordinates</label>
                <input type="text" id="coordinates" class="form-control"
                    placeholder="29-20.174N 094-36.404W">
                <small class="text-muted">Format: DD-MM.MMMD DDD-MM.MMMD (e.g., 29-20.174N 094-36.404W)</small>
            </div>`;
    }
    
    // Add additional fields based on type
    if (template.rotation !== undefined) {
        formHTML += `
            <div class="col-md-3 mb-2">
                <label class="form-label">Rotation</label>
                <input type="number" id="rotation" class="form-control" value="${template.rotation}">
            </div>`;
    }
    
    if (template.size !== undefined) {
        formHTML += `
            <div class="col-md-3 mb-2">
                <label class="form-label">Font Size</label>
                <input type="number" id="size" class="form-control" value="${template.size}" min="6" max="72">
                <small class="text-muted">6-72</small>
            </div>`;
    }
    
    if (template.radius !== undefined) {
        formHTML += `
            <div class="col-md-3 mb-2">
                <label class="form-label">Radius (nm)</label>
                <input type="number" id="radius" class="form-control" step="0.01" value="${template.radius}">
            </div>`;
    }

    if (template.horizontalRadius !== undefined) {
        formHTML += `
            <div class="col-md-3 mb-2">
                <label class="form-label">Horizontal Radius (nm)</label>
                <input type="number" id="horizontalRadius" class="form-control" step="0.01" value="${template.horizontalRadius}">
            </div>`;
    }

    if (template.verticalRadius !== undefined) {
        formHTML += `
            <div class="col-md-3 mb-2">
                <label class="form-label">Vertical Radius (nm)</label>
                <input type="number" id="verticalRadius" class="form-control" step="0.01" value="${template.verticalRadius}">
            </div>`;
    }
    
    if (template.startAngle !== undefined) {
        formHTML += `
            <div class="col-md-3 mb-2">
                <label class="form-label">Start Angle</label>
                <input type="number" id="startAngle" class="form-control" value="${template.startAngle}">
            </div>`;
    }
    
    if (template.endAngle !== undefined) {
        formHTML += `
            <div class="col-md-3 mb-2">
                <label class="form-label">End Angle</label>
                <input type="number" id="endAngle" class="form-control" value="${template.endAngle}">
            </div>`;
    }
    
    formHTML += '</div>';
    formContainer.innerHTML = formHTML;
}

// These functions are no longer needed with the new coordinate input method

// Add new entry
function addEntry() {
    const entryType = document.getElementById('entryType').value;
    if (!entryType) return;

    // Validation
    const validationErrors = [];

    // Validate text field
    const textElement = document.getElementById('text');
    if (textElement) {
        const textValidation = validateText(textElement.value, 'Text');
        if (!textValidation.isValid) {
            validationErrors.push(textValidation.error);
        }
    }

    // Validate comment field
    const commentElement = document.getElementById('comment');
    if (commentElement) {
        const commentValidation = validateText(commentElement.value, 'Comment');
        if (!commentValidation.isValid) {
            validationErrors.push(commentValidation.error);
        }
    }

    // Validate font size
    const sizeElement = document.getElementById('size');
    if (sizeElement && sizeElement.value) {
        const sizeValidation = validateFontSize(sizeElement.value);
        if (!sizeValidation.isValid) {
            validationErrors.push(sizeValidation.error);
        }
    }

    // Validate coordinates
    const coordinatesInput = document.getElementById('coordinates')?.value;
    let coordinateValidation = { isValid: true, errors: [], coordinates: [] };
    if (coordinatesInput) {
        coordinateValidation = validateCoordinates(coordinatesInput);
        if (!coordinateValidation.isValid) {
            validationErrors.push(...coordinateValidation.errors);
        }
    }

    // Show validation errors if any
    if (validationErrors.length > 0) {
        alert('Validation Errors:\n\n' + validationErrors.join('\n'));
        return;
    }

    const entry = {
        id: ++entryIdCounter,
        type: entryType,
        data: {}
    };

    // Collect form data based on entry type
    const template = entryTypes[entryType].template;

    if (template.instName !== undefined) {
        entry.data.instName = document.getElementById('instName').value;
    }

    if (template.text !== undefined) {
        entry.data.text = textElement.value;
    }

    entry.data.comment = commentElement.value;

    // Handle coordinates
    if (coordinateValidation.coordinates.length > 0) {
        entry.data.coordinates = coordinateValidation.coordinates;
    }

    // Handle additional fields
    ['rotation', 'size', 'radius', 'horizontalRadius', 'verticalRadius', 'startAngle', 'endAngle'].forEach(field => {
        const element = document.getElementById(field);
        if (element) {
            entry.data[field] = parseFloat(element.value) || 0;
        }
    });

    entries.push(entry);
    renderEntries();
    updateEntryCount();

    // Reset form
    document.getElementById('entryType').value = '';
    updateEntryForm();
}

// Render all entries
function renderEntries() {
    const container = document.getElementById('entriesContainer');
    container.innerHTML = '';
    
    entries.forEach(entry => {
        const entryDiv = document.createElement('div');
        entryDiv.className = 'card entry-card';
        entryDiv.innerHTML = createEntryHTML(entry);
        container.appendChild(entryDiv);
    });
}

// Create HTML for a single entry
function createEntryHTML(entry) {
    let html = `
        <div class="card-header entry-header">
            <span class="entry-type">${entry.type}</span>
            <div class="action-buttons">
                <button class="btn btn-sm btn-outline-primary" onclick="editEntry(${entry.id})">
                    Edit
                </button>
                <button class="btn btn-sm btn-outline-danger delete-btn" onclick="deleteEntry(${entry.id})">
                    Delete
                </button>
            </div>
        </div>
        <div class="card-body" id="entry-body-${entry.id}">
    `;
    
    // Display entry-specific data
    if (entry.data.instName) {
        html += `<p><strong>Instance Name:</strong> ${entry.data.instName}</p>`;
    }
    
    if (entry.data.text) {
        html += `<p><strong>Text:</strong> ${entry.data.text}</p>`;
    }
    
    if (entry.data.comment) {
        html += `<p><strong>Comment:</strong> ${entry.data.comment}</p>`;
    }
    
    // Display coordinates
    if (entry.data.coordinates && entry.data.coordinates.length > 0) {
        html += '<p><strong>Coordinates:</strong></p><ul>';
        entry.data.coordinates.forEach(coord => {
            html += `<li>${coord.lat},${coord.latDir} ${coord.lon},${coord.lonDir}</li>`;
        });
        html += '</ul>';
    }
    
    // Display additional parameters
    ['rotation', 'size', 'radius', 'horizontalRadius', 'verticalRadius', 'startAngle', 'endAngle'].forEach(field => {
        if (entry.data[field] !== undefined) {
            let label = field.charAt(0).toUpperCase() + field.slice(1);
            if (field === 'horizontalRadius') label = 'Horizontal Radius';
            if (field === 'verticalRadius') label = 'Vertical Radius';
            if (field === 'startAngle') label = 'Start Angle';
            if (field === 'endAngle') label = 'End Angle';
            html += `<p><strong>${label}:</strong> ${entry.data[field]}</p>`;
        }
    });
    
    html += '</div>';
    return html;
}

// Delete entry
function deleteEntry(id) {
    if (confirm('Are you sure you want to delete this entry?')) {
        entries = entries.filter(entry => entry.id !== id);
        renderEntries();
        updateEntryCount();
    }
}

// Clear all data
function clearAllData() {
    if (confirm('Are you sure you want to clear all data? This action cannot be undone.')) {
        entries = [];
        entryIdCounter = 0;
        renderEntries();
        updateEntryCount();
    }
}

// Edit entry
function editEntry(id) {
    const entry = entries.find(e => e.id === id);
    if (!entry) return;

    const entryCard = document.querySelector(`#entry-body-${id}`).parentElement;
    entryCard.classList.add('edit-mode');

    const bodyElement = document.getElementById(`entry-body-${id}`);
    bodyElement.innerHTML = createEditForm(entry);
}

// Create edit form for an entry
function createEditForm(entry) {
    let formHTML = '<div class="edit-form">';

    // Entry type (read-only)
    formHTML += `<div class="mb-2"><strong>Type:</strong> ${entry.type}</div>`;

    // Instance Name
    if (entry.data.instName !== undefined) {
        if (entry.type === 'SYMBOL') {
            formHTML += `
                <div class="mb-2">
                    <label class="form-label">Symbol Type</label>
                    <select id="edit-instName-${entry.id}" class="form-select">`;
            Object.entries(symbolTypes).forEach(([value, label]) => {
                const selected = value === entry.data.instName ? 'selected' : '';
                formHTML += `<option value="${value}" ${selected}>${label} (${value})</option>`;
            });
            formHTML += `
                    </select>
                </div>`;
        } else {
            formHTML += `
                <div class="mb-2">
                    <label class="form-label">Instance Name</label>
                    <input type="text" id="edit-instName-${entry.id}" class="form-control" value="${entry.data.instName || ''}">
                </div>`;
        }
    }

    // Text
    if (entry.data.text !== undefined) {
        formHTML += `
            <div class="mb-2">
                <label class="form-label">Text</label>
                <input type="text" id="edit-text-${entry.id}" class="form-control" value="${entry.data.text || ''}" maxlength="63">
                <small class="text-muted">Max 63 characters</small>
            </div>`;
    }

    // Comment
    formHTML += `
        <div class="mb-2">
            <label class="form-label">Comment</label>
            <input type="text" id="edit-comment-${entry.id}" class="form-control" value="${entry.data.comment || ''}" maxlength="63">
            <small class="text-muted">Max 63 characters</small>
        </div>`;

    // Coordinates
    if (entry.data.coordinates && entry.data.coordinates.length > 0) {
        const coordinateText = formatCoordinateForUser(entry.data.coordinates);

        if (['POLYGON', 'LINE', 'DANGER_LINE', 'DANGER_AREA'].includes(entry.type)) {
            formHTML += `
                <div class="mb-2">
                    <label class="form-label">Coordinates (Multiple)</label>
                    <textarea id="edit-coordinates-${entry.id}" class="form-control" rows="3">${coordinateText}</textarea>
                    <small class="text-muted">Format: DD-MM.MMMD DDD-MM.MMMD (e.g., 29-20.174N 094-36.404W)</small>
                </div>`;
        } else {
            formHTML += `
                <div class="mb-2">
                    <label class="form-label">Coordinates</label>
                    <input type="text" id="edit-coordinates-${entry.id}" class="form-control" value="${coordinateText}">
                    <small class="text-muted">Format: DD-MM.MMMD DDD-MM.MMMD (e.g., 29-20.174N 094-36.404W)</small>
                </div>`;
        }
    }

    // Additional fields
    if (entry.data.rotation !== undefined) {
        formHTML += `
            <div class="mb-2">
                <label class="form-label">Rotation</label>
                <input type="number" id="edit-rotation-${entry.id}" class="form-control" value="${entry.data.rotation}">
            </div>`;
    }

    if (entry.data.size !== undefined) {
        formHTML += `
            <div class="mb-2">
                <label class="form-label">Font Size</label>
                <input type="number" id="edit-size-${entry.id}" class="form-control" value="${entry.data.size}" min="6" max="72">
                <small class="text-muted">6-72</small>
            </div>`;
    }

    if (entry.data.radius !== undefined) {
        formHTML += `
            <div class="mb-2">
                <label class="form-label">Radius (nm)</label>
                <input type="number" id="edit-radius-${entry.id}" class="form-control" step="0.01" value="${entry.data.radius}">
            </div>`;
    }

    if (entry.data.horizontalRadius !== undefined) {
        formHTML += `
            <div class="mb-2">
                <label class="form-label">Horizontal Radius (nm)</label>
                <input type="number" id="edit-horizontalRadius-${entry.id}" class="form-control" step="0.01" value="${entry.data.horizontalRadius}">
            </div>`;
    }

    if (entry.data.verticalRadius !== undefined) {
        formHTML += `
            <div class="mb-2">
                <label class="form-label">Vertical Radius (nm)</label>
                <input type="number" id="edit-verticalRadius-${entry.id}" class="form-control" step="0.01" value="${entry.data.verticalRadius}">
            </div>`;
    }

    if (entry.data.startAngle !== undefined) {
        formHTML += `
            <div class="mb-2">
                <label class="form-label">Start Angle</label>
                <input type="number" id="edit-startAngle-${entry.id}" class="form-control" value="${entry.data.startAngle}">
            </div>`;
    }

    if (entry.data.endAngle !== undefined) {
        formHTML += `
            <div class="mb-2">
                <label class="form-label">End Angle</label>
                <input type="number" id="edit-endAngle-${entry.id}" class="form-control" value="${entry.data.endAngle}">
            </div>`;
    }

    // Action buttons
    formHTML += `
        <div class="mt-3">
            <button class="btn btn-success me-2" onclick="saveEntry(${entry.id})">Save</button>
            <button class="btn btn-secondary" onclick="cancelEdit()">Cancel</button>
        </div>
    </div>`;

    return formHTML;
}

// Save edited entry
function saveEntry(id) {
    const entry = entries.find(e => e.id === id);
    if (!entry) return;

    // Validation
    const validationErrors = [];

    // Validate text field
    const textEl = document.getElementById(`edit-text-${id}`);
    if (textEl) {
        const textValidation = validateText(textEl.value, 'Text');
        if (!textValidation.isValid) {
            validationErrors.push(textValidation.error);
        }
    }

    // Validate comment field
    const commentEl = document.getElementById(`edit-comment-${id}`);
    if (commentEl) {
        const commentValidation = validateText(commentEl.value, 'Comment');
        if (!commentValidation.isValid) {
            validationErrors.push(commentValidation.error);
        }
    }

    // Validate font size
    const sizeEl = document.getElementById(`edit-size-${id}`);
    if (sizeEl && sizeEl.value) {
        const sizeValidation = validateFontSize(sizeEl.value);
        if (!sizeValidation.isValid) {
            validationErrors.push(sizeValidation.error);
        }
    }

    // Validate coordinates
    const coordInput = document.getElementById(`edit-coordinates-${id}`);
    let coordinateValidation = { isValid: true, errors: [], coordinates: [] };
    if (coordInput && coordInput.value) {
        coordinateValidation = validateCoordinates(coordInput.value);
        if (!coordinateValidation.isValid) {
            validationErrors.push(...coordinateValidation.errors);
        }
    }

    // Show validation errors if any
    if (validationErrors.length > 0) {
        alert('Validation Errors:\n\n' + validationErrors.join('\n'));
        return;
    }

    // Update entry data from form
    const instNameEl = document.getElementById(`edit-instName-${id}`);
    if (instNameEl) entry.data.instName = instNameEl.value;

    if (textEl) entry.data.text = textEl.value;
    if (commentEl) entry.data.comment = commentEl.value;

    // Update coordinates
    if (coordinateValidation.coordinates.length > 0) {
        entry.data.coordinates = coordinateValidation.coordinates;
    }

    // Update additional fields
    ['rotation', 'size', 'radius', 'horizontalRadius', 'verticalRadius', 'startAngle', 'endAngle'].forEach(field => {
        const element = document.getElementById(`edit-${field}-${id}`);
        if (element) {
            entry.data[field] = parseFloat(element.value) || 0;
        }
    });

    renderEntries();
}

// Cancel edit
function cancelEdit() {
    renderEntries();
}

// These functions are no longer needed with the new coordinate input method

// Update entry count
function updateEntryCount() {
    document.getElementById('entryCount').textContent = entries.length;
}

// Export data to file
function exportData() {
    // CSV Header
    let csvContent = 'Type,InstName,Text,Comment,Lat,LatDir,Lon,LonDir,Rotation,Size,Radius,HorizontalRadius,VerticalRadius,StartAngle,EndAngle,AdditionalCoords\n';

    entries.forEach(entry => {
        csvContent += generateCSVRow(entry);
    });

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'umap_export.csv';
    a.click();
    URL.revokeObjectURL(url);
}

// Generate CSV row for an entry
function generateCSVRow(entry) {
    const escapeCSV = (str) => {
        if (!str) return '';
        // Escape quotes and wrap in quotes if contains comma, quote, or newline
        if (str.includes(',') || str.includes('"') || str.includes('\n')) {
            return '"' + str.replace(/"/g, '""') + '"';
        }
        return str;
    };

    let row = [];

    // Type
    row.push(entry.type);

    // InstName
    row.push(escapeCSV(entry.data.instName || ''));

    // Text
    row.push(escapeCSV(entry.data.text || ''));

    // Comment
    row.push(escapeCSV(entry.data.comment || ''));

    // Primary coordinates
    if (entry.data.coordinates && entry.data.coordinates[0]) {
        const coord = entry.data.coordinates[0];
        row.push(coord.lat || '');
        row.push(coord.latDir || '');
        row.push(coord.lon || '');
        row.push(coord.lonDir || '');
    } else {
        row.push('', '', '', '');
    }

    // Rotation
    row.push(entry.data.rotation !== undefined ? entry.data.rotation : '');

    // Size
    row.push(entry.data.size !== undefined ? entry.data.size : '');

    // Radius
    row.push(entry.data.radius !== undefined ? entry.data.radius : '');

    // Horizontal Radius
    row.push(entry.data.horizontalRadius !== undefined ? entry.data.horizontalRadius : '');

    // Vertical Radius
    row.push(entry.data.verticalRadius !== undefined ? entry.data.verticalRadius : '');

    // Start Angle
    row.push(entry.data.startAngle !== undefined ? entry.data.startAngle : '');

    // End Angle
    row.push(entry.data.endAngle !== undefined ? entry.data.endAngle : '');

    // Additional coordinates (for polygons, lines, etc.)
    if (entry.data.coordinates && entry.data.coordinates.length > 1) {
        const additionalCoords = entry.data.coordinates.slice(1).map(coord =>
            `${coord.lat},${coord.latDir},${coord.lon},${coord.lonDir}`
        ).join(';');
        row.push(escapeCSV(additionalCoords));
    } else {
        row.push('');
    }

    return row.join(',') + '\n';
}

// Load sample data from the user_map.csv file
function loadSampleData() {
    fetch('user_map.csv')
        .then(response => response.text())
        .then(content => {
            parseImportedData(content);
        })
        .catch(error => {
            console.error('Error loading sample data:', error);
            // Fallback to hardcoded sample data
            entries = [
                {
                    id: ++entryIdCounter,
                    type: 'SYMBOL',
                    data: {
                        instName: '~WARNSY6',
                        comment: 'Comment',
                        coordinates: [{ lat: '29,19.904', latDir: 'N', lon: '094,36.289', lonDir: 'W' }]
                    }
                },
                {
                    id: ++entryIdCounter,
                    type: 'TEXT',
                    data: {
                        text: 'Text Displayed',
                        comment: 'Comment',
                        coordinates: [{ lat: '29,20.598', latDir: 'N', lon: '094,36.425', lonDir: 'W' }],
                        rotation: 90,
                        size: 11
                    }
                },
                {
                    id: ++entryIdCounter,
                    type: 'ELLIPSE',
                    data: {
                        comment: 'Comment',
                        coordinates: [{ lat: '29,18.988', latDir: 'N', lon: '094,35.694', lonDir: 'W' }],
                        horizontalRadius: 0.14,
                        verticalRadius: 0.02
                    }
                }
            ];

            renderEntries();
            updateEntryCount();
            alert('Loaded fallback sample data. To load your actual file, use the Import File feature.');
        });
}

// Import file
function importFile(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = function(e) {
        const content = e.target.result;
        parseImportedData(content);
    };
    reader.readAsText(file);
}

// Parse imported data
function parseImportedData(content) {
    entries = [];
    entryIdCounter = 0;

    // Check if it's CSV format
    if (content.includes('Type,InstName,Text,Comment') || content.split('\n')[0].includes(',')) {
        parseCSVData(content);
    } else {
        // Parse original text format
        parseTextData(content);
    }

    renderEntries();
    updateEntryCount();
    alert(`Successfully imported ${entries.length} entries!`);
}

// Parse CSV format data
function parseCSVData(content) {
    const lines = content.split('\n').map(line => line.trim()).filter(line => line);

    if (lines.length === 0) return;

    // Skip header if present
    let startIndex = 0;
    if (lines[0].includes('Type,InstName,Text,Comment') || lines[0].includes('Type,')) {
        startIndex = 1;
    }

    for (let i = startIndex; i < lines.length; i++) {
        const row = parseCSVRow(lines[i]);
        if (row.length >= 4 && row[0]) { // Must have at least type
            const entry = {
                id: ++entryIdCounter,
                type: row[0],
                data: {
                    instName: row[1] || '',
                    text: row[2] || '',
                    comment: row[3] || '',
                    coordinates: []
                }
            };

            // Parse primary coordinates
            if (row[4] && row[6]) { // lat and lon
                entry.data.coordinates.push({
                    lat: row[4],
                    latDir: row[5] || 'N',
                    lon: row[6],
                    lonDir: row[7] || 'W'
                });
            }

            // Parse additional fields
            if (row[8]) entry.data.rotation = parseFloat(row[8]);
            if (row[9]) entry.data.size = parseFloat(row[9]);
            if (row[10]) entry.data.radius = parseFloat(row[10]);
            if (row[11]) entry.data.horizontalRadius = parseFloat(row[11]);
            if (row[12]) entry.data.verticalRadius = parseFloat(row[12]);
            if (row[13]) entry.data.startAngle = parseFloat(row[13]);
            if (row[14]) entry.data.endAngle = parseFloat(row[14]);

            // Parse additional coordinates
            if (row[15]) {
                const additionalCoords = row[15].split(';');
                additionalCoords.forEach(coordStr => {
                    const parts = coordStr.split(',');
                    if (parts.length >= 4) {
                        entry.data.coordinates.push({
                            lat: parts[0],
                            latDir: parts[1],
                            lon: parts[2],
                            lonDir: parts[3]
                        });
                    }
                });
            }

            entries.push(entry);
        }
    }
}

// Parse CSV row handling quoted values
function parseCSVRow(line) {
    const result = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
        const char = line[i];

        if (char === '"') {
            if (inQuotes && line[i + 1] === '"') {
                // Escaped quote
                current += '"';
                i++; // Skip next quote
            } else {
                // Toggle quote state
                inQuotes = !inQuotes;
            }
        } else if (char === ',' && !inQuotes) {
            // End of field
            result.push(current);
            current = '';
        } else {
            current += char;
        }
    }

    // Add the last field
    result.push(current);

    return result;
}

// Parse original text format data
function parseTextData(content) {
    const lines = content.split('\n').map(line => line.trim());

    let i = 0;
    while (i < lines.length) {
        const line = lines[i];

        // Skip comments and empty lines
        if (line.startsWith('//') || line === '') {
            i++;
            continue;
        }

        // Parse different entry types
        if (line.startsWith('SYMBOL,')) {
            i = parseSymbolEntry(lines, i);
        } else if (line.startsWith('TEXT,')) {
            i = parseTextEntry(lines, i);
        } else if (line === 'POLYGON') {
            i = parsePolygonEntry(lines, i);
        } else if (line.startsWith('LINE,')) {
            i = parseLineEntry(lines, i);
        } else if (line === 'CIRCLE') {
            i = parseCircleEntry(lines, i);
        } else if (line === 'ELLIPSE') {
            i = parseEllipseEntry(lines, i);
        } else if (line === 'FAN') {
            i = parseFanEntry(lines, i);
        } else if (line === 'ARC') {
            i = parseArcEntry(lines, i);
        } else if (line === 'DANGER_LINE') {
            i = parseDangerLineEntry(lines, i);
        } else if (line === 'DANGER_AREA') {
            i = parseDangerAreaEntry(lines, i);
        } else {
            i++;
        }
    }
}

// Parse SYMBOL entry
function parseSymbolEntry(lines, startIndex) {
    const symbolLine = lines[startIndex];
    const parts = symbolLine.split(',');
    const instName = parts[1] || '***';

    const comment = lines[startIndex + 1] || '';
    const coordLine = lines[startIndex + 2] || '';

    const entry = {
        id: ++entryIdCounter,
        type: 'SYMBOL',
        data: {
            instName: instName,
            comment: comment,
            coordinates: parseCoordinateLine(coordLine)
        }
    };

    entries.push(entry);
    return startIndex + 3;
}

// Parse TEXT entry
function parseTextEntry(lines, startIndex) {
    const textLine = lines[startIndex];
    const parts = textLine.split(',');
    const text = parts[1] || '';

    const comment = lines[startIndex + 1] || '';
    const coordLine = lines[startIndex + 2] || '';
    const coordParts = coordLine.split(',');

    const entry = {
        id: ++entryIdCounter,
        type: 'TEXT',
        data: {
            text: text,
            comment: comment,
            coordinates: parseCoordinateLine(coordLine),
            rotation: parseInt(coordParts[4]) || 0,
            size: parseInt(coordParts[5]) || 12
        }
    };

    entries.push(entry);
    return startIndex + 3;
}

// Parse POLYGON entry
function parsePolygonEntry(lines, startIndex) {
    const comment = lines[startIndex + 1] || '';
    const coordinates = [];

    let i = startIndex + 2;
    while (i < lines.length && lines[i] !== 'END') {
        if (lines[i].trim() !== '') {
            const coords = parseCoordinateLine(lines[i]);
            if (coords.length > 0) {
                coordinates.push(coords[0]);
            }
        }
        i++;
    }

    const entry = {
        id: ++entryIdCounter,
        type: 'POLYGON',
        data: {
            comment: comment,
            coordinates: coordinates
        }
    };

    entries.push(entry);
    return i + 1; // Skip the END line
}

// Parse LINE entry
function parseLineEntry(lines, startIndex) {
    const lineLine = lines[startIndex];
    const parts = lineLine.split(',');
    const instName = parts[1] || '***';

    const comment = lines[startIndex + 1] || '';
    const coordinates = [];

    let i = startIndex + 2;
    while (i < lines.length && lines[i] !== 'END') {
        if (lines[i].trim() !== '') {
            const coords = parseCoordinateLine(lines[i]);
            if (coords.length > 0) {
                coordinates.push(coords[0]);
            }
        }
        i++;
    }

    const entry = {
        id: ++entryIdCounter,
        type: 'LINE',
        data: {
            instName: instName,
            comment: comment,
            coordinates: coordinates
        }
    };

    entries.push(entry);
    return i + 1; // Skip the END line
}

// Parse CIRCLE entry
function parseCircleEntry(lines, startIndex) {
    const comment = lines[startIndex + 1] || '';
    const coordLine = lines[startIndex + 2] || '';
    const parts = coordLine.split(',');

    const entry = {
        id: ++entryIdCounter,
        type: 'CIRCLE',
        data: {
            comment: comment,
            coordinates: parseCoordinateLine(coordLine),
            radius: parseFloat(parts[4]) || 0.1
        }
    };

    entries.push(entry);
    return startIndex + 3;
}

// Parse ELLIPSE entry
function parseEllipseEntry(lines, startIndex) {
    const comment = lines[startIndex + 1] || '';
    const coordLine = lines[startIndex + 2] || '';
    const parts = coordLine.split(',');

    const entry = {
        id: ++entryIdCounter,
        type: 'ELLIPSE',
        data: {
            comment: comment,
            coordinates: parseCoordinateLine(coordLine),
            horizontalRadius: parseFloat(parts[4]) || 0.14,
            verticalRadius: parseFloat(parts[5]) || 0.02
        }
    };

    entries.push(entry);
    return startIndex + 3;
}

// Parse FAN entry
function parseFanEntry(lines, startIndex) {
    const comment = lines[startIndex + 1] || '';
    const coordLine = lines[startIndex + 2] || '';
    const parts = coordLine.split(',');

    const entry = {
        id: ++entryIdCounter,
        type: 'FAN',
        data: {
            comment: comment,
            coordinates: parseCoordinateLine(coordLine),
            radius: parseFloat(parts[4]) || 1.0,
            startAngle: parseFloat(parts[5]) || 0,
            endAngle: parseFloat(parts[6]) || 90
        }
    };

    entries.push(entry);
    return startIndex + 3;
}

// Parse ARC entry
function parseArcEntry(lines, startIndex) {
    const comment = lines[startIndex + 1] || '';
    const coordLine = lines[startIndex + 2] || '';
    const parts = coordLine.split(',');

    const entry = {
        id: ++entryIdCounter,
        type: 'ARC',
        data: {
            comment: comment,
            coordinates: parseCoordinateLine(coordLine),
            radius: parseFloat(parts[4]) || 1.0,
            startAngle: parseFloat(parts[5]) || 0,
            endAngle: parseFloat(parts[6]) || 90
        }
    };

    entries.push(entry);
    return startIndex + 3;
}

// Parse DANGER_LINE entry
function parseDangerLineEntry(lines, startIndex) {
    const comment = lines[startIndex + 1] || '';
    const coordinates = [];

    let i = startIndex + 2;
    while (i < lines.length && lines[i] !== 'END') {
        if (lines[i].trim() !== '') {
            const coords = parseCoordinateLine(lines[i]);
            if (coords.length > 0) {
                coordinates.push(coords[0]);
            }
        }
        i++;
    }

    const entry = {
        id: ++entryIdCounter,
        type: 'DANGER_LINE',
        data: {
            comment: comment,
            coordinates: coordinates
        }
    };

    entries.push(entry);
    return i + 1; // Skip the END line
}

// Parse DANGER_AREA entry
function parseDangerAreaEntry(lines, startIndex) {
    const comment = lines[startIndex + 1] || '';
    const coordinates = [];

    let i = startIndex + 2;
    while (i < lines.length && lines[i] !== 'END') {
        if (lines[i].trim() !== '') {
            const coords = parseCoordinateLine(lines[i]);
            if (coords.length > 0) {
                coordinates.push(coords[0]);
            }
        }
        i++;
    }

    const entry = {
        id: ++entryIdCounter,
        type: 'DANGER_AREA',
        data: {
            comment: comment,
            coordinates: coordinates
        }
    };

    entries.push(entry);
    return i + 1; // Skip the END line
}

// Parse coordinate line from ECDIS text format
function parseCoordinateLine(line) {
    if (!line || line.trim() === '') return [];

    const parts = line.split(',');
    if (parts.length >= 6) {
        // Format: 29,20.174,N,094,36.404,W
        const latDeg = parts[0].padStart(2, '0');
        const latMin = parseFloat(parts[1]).toFixed(3).padStart(6, '0');
        const lonDeg = parts[3].padStart(3, '0');
        const lonMin = parseFloat(parts[4]).toFixed(3).padStart(6, '0');

        return [{
            lat: `${latDeg},${latMin}`,
            latDir: parts[2],
            lon: `${lonDeg},${lonMin}`,
            lonDir: parts[5] || 'W'
        }];
    }
    return [];
}

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    updateEntryCount();
});
