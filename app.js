// Global data storage
let entries = [];
let entryIdCounter = 0;

// Entry type definitions with their required fields
const entryTypes = {
    SYMBOL: {
        fields: ['instName', 'comment', 'lat', 'latDir', 'lon', 'lonDir'],
        template: {
            instName: '~WARNSY6',
            comment: '',
            coordinates: [{ lat: '', latDir: 'N', lon: '', lonDir: 'W' }]
        }
    },
    TEXT: {
        fields: ['text', 'comment', 'lat', 'latDir', 'lon', 'lonDir', 'rotation', 'size'],
        template: {
            text: '',
            comment: '',
            coordinates: [{ lat: '', latDir: 'N', lon: '', lonDir: 'W' }],
            rotation: 0,
            size: 12
        }
    },
    POLYGON: {
        fields: ['comment', 'coordinates'],
        template: {
            comment: '',
            coordinates: [
                { lat: '', latDir: 'N', lon: '', lonDir: 'W' }
            ]
        }
    },
    LINE: {
        fields: ['instName', 'comment', 'coordinates'],
        template: {
            instName: '***',
            comment: '',
            coordinates: [
                { lat: '', latDir: 'N', lon: '', lonDir: 'W' }
            ]
        }
    },
    CIRCLE: {
        fields: ['comment', 'lat', 'latDir', 'lon', 'lonDir', 'radius'],
        template: {
            comment: '',
            coordinates: [{ lat: '', latDir: 'N', lon: '', lonDir: 'W' }],
            radius: 1.0
        }
    },
    FAN: {
        fields: ['comment', 'lat', 'latDir', 'lon', 'lonDir', 'radius', 'startAngle', 'endAngle'],
        template: {
            comment: '',
            coordinates: [{ lat: '', latDir: 'N', lon: '', lonDir: 'W' }],
            radius: 1.0,
            startAngle: 0,
            endAngle: 90
        }
    },
    ARC: {
        fields: ['comment', 'lat', 'latDir', 'lon', 'lonDir', 'radius', 'startAngle', 'endAngle'],
        template: {
            comment: '',
            coordinates: [{ lat: '', latDir: 'N', lon: '', lonDir: 'W' }],
            radius: 1.0,
            startAngle: 0,
            endAngle: 90
        }
    },
    DANGER_LINE: {
        fields: ['comment', 'coordinates'],
        template: {
            comment: '',
            coordinates: [
                { lat: '', latDir: 'N', lon: '', lonDir: 'W' }
            ]
        }
    },
    DANGER_AREA: {
        fields: ['comment', 'coordinates'],
        template: {
            comment: '',
            coordinates: [
                { lat: '', latDir: 'N', lon: '', lonDir: 'W' }
            ]
        }
    }
};

// Update entry form based on selected type
function updateEntryForm() {
    const entryType = document.getElementById('entryType').value;
    const formContainer = document.getElementById('entryForm');
    const addBtn = document.getElementById('addEntryBtn');
    
    if (!entryType) {
        formContainer.innerHTML = '';
        addBtn.disabled = true;
        return;
    }
    
    addBtn.disabled = false;
    const template = entryTypes[entryType].template;
    
    let formHTML = '<div class="row">';
    
    // Add specific fields based on entry type
    if (template.instName !== undefined) {
        formHTML += `
            <div class="col-md-6 mb-2">
                <label class="form-label">Instance Name</label>
                <input type="text" id="instName" class="form-control" value="${template.instName}">
            </div>`;
    }
    
    if (template.text !== undefined) {
        formHTML += `
            <div class="col-md-6 mb-2">
                <label class="form-label">Text</label>
                <input type="text" id="text" class="form-control" placeholder="Enter text">
            </div>`;
    }
    
    formHTML += `
        <div class="col-md-12 mb-2">
            <label class="form-label">Comment</label>
            <input type="text" id="comment" class="form-control" placeholder="Enter comment">
        </div>`;
    
    // Add coordinate fields
    if (entryType === 'POLYGON' || entryType === 'LINE' || entryType === 'DANGER_LINE' || entryType === 'DANGER_AREA') {
        formHTML += `
            <div class="col-md-12 mb-2">
                <label class="form-label">Coordinates</label>
                <div id="coordinatesContainer">
                    <div class="coordinate-group">
                        <input type="text" class="form-control coordinate-input" placeholder="Lat">
                        <select class="form-select" style="width: 60px;">
                            <option value="N">N</option>
                            <option value="S">S</option>
                        </select>
                        <input type="text" class="form-control coordinate-input" placeholder="Lon">
                        <select class="form-select" style="width: 60px;">
                            <option value="W" selected>W</option>
                            <option value="E">E</option>
                        </select>
                        <button type="button" class="btn btn-sm btn-danger" onclick="removeCoordinate(this)">
                            <i class="fas fa-minus"></i>
                        </button>
                    </div>
                </div>
                <button type="button" class="btn btn-sm btn-secondary mt-2" onclick="addCoordinate()">
                    <i class="fas fa-plus"></i> Add Coordinate
                </button>
            </div>`;
    } else {
        formHTML += `
            <div class="col-md-3 mb-2">
                <label class="form-label">Latitude</label>
                <input type="text" id="lat" class="form-control" placeholder="17,48.000">
            </div>
            <div class="col-md-2 mb-2">
                <label class="form-label">Dir</label>
                <select id="latDir" class="form-select">
                    <option value="N">N</option>
                    <option value="S">S</option>
                </select>
            </div>
            <div class="col-md-3 mb-2">
                <label class="form-label">Longitude</label>
                <input type="text" id="lon" class="form-control" placeholder="055,56.000">
            </div>
            <div class="col-md-2 mb-2">
                <label class="form-label">Dir</label>
                <select id="lonDir" class="form-select">
                    <option value="W" selected>W</option>
                    <option value="E">E</option>
                </select>
            </div>`;
    }
    
    // Add additional fields based on type
    if (template.rotation !== undefined) {
        formHTML += `
            <div class="col-md-3 mb-2">
                <label class="form-label">Rotation</label>
                <input type="number" id="rotation" class="form-control" value="${template.rotation}">
            </div>`;
    }
    
    if (template.size !== undefined) {
        formHTML += `
            <div class="col-md-3 mb-2">
                <label class="form-label">Size</label>
                <input type="number" id="size" class="form-control" value="${template.size}">
            </div>`;
    }
    
    if (template.radius !== undefined) {
        formHTML += `
            <div class="col-md-3 mb-2">
                <label class="form-label">Radius (nm)</label>
                <input type="number" id="radius" class="form-control" step="0.1" value="${template.radius}">
            </div>`;
    }
    
    if (template.startAngle !== undefined) {
        formHTML += `
            <div class="col-md-3 mb-2">
                <label class="form-label">Start Angle</label>
                <input type="number" id="startAngle" class="form-control" value="${template.startAngle}">
            </div>`;
    }
    
    if (template.endAngle !== undefined) {
        formHTML += `
            <div class="col-md-3 mb-2">
                <label class="form-label">End Angle</label>
                <input type="number" id="endAngle" class="form-control" value="${template.endAngle}">
            </div>`;
    }
    
    formHTML += '</div>';
    formContainer.innerHTML = formHTML;
}

// Add coordinate input group
function addCoordinate() {
    const container = document.getElementById('coordinatesContainer');
    const newCoord = document.createElement('div');
    newCoord.className = 'coordinate-group';
    newCoord.innerHTML = `
        <input type="text" class="form-control coordinate-input" placeholder="Lat">
        <select class="form-select" style="width: 60px;">
            <option value="N">N</option>
            <option value="S">S</option>
        </select>
        <input type="text" class="form-control coordinate-input" placeholder="Lon">
        <select class="form-select" style="width: 60px;">
            <option value="W" selected>W</option>
            <option value="E">E</option>
        </select>
        <button type="button" class="btn btn-sm btn-danger" onclick="removeCoordinate(this)">
            <i class="fas fa-minus"></i>
        </button>
    `;
    container.appendChild(newCoord);
}

// Remove coordinate input group
function removeCoordinate(button) {
    const container = document.getElementById('coordinatesContainer');
    if (container.children.length > 1) {
        button.parentElement.remove();
    }
}

// Add new entry
function addEntry() {
    const entryType = document.getElementById('entryType').value;
    if (!entryType) return;
    
    const entry = {
        id: ++entryIdCounter,
        type: entryType,
        data: {}
    };
    
    // Collect form data based on entry type
    const template = entryTypes[entryType].template;
    
    if (template.instName !== undefined) {
        entry.data.instName = document.getElementById('instName').value;
    }
    
    if (template.text !== undefined) {
        entry.data.text = document.getElementById('text').value;
    }
    
    entry.data.comment = document.getElementById('comment').value;
    
    // Handle coordinates
    if (entryType === 'POLYGON' || entryType === 'LINE' || entryType === 'DANGER_LINE' || entryType === 'DANGER_AREA') {
        entry.data.coordinates = [];
        const coordGroups = document.querySelectorAll('#coordinatesContainer .coordinate-group');
        coordGroups.forEach(group => {
            const inputs = group.querySelectorAll('input');
            const selects = group.querySelectorAll('select');
            if (inputs[0].value && inputs[1].value) {
                entry.data.coordinates.push({
                    lat: inputs[0].value,
                    latDir: selects[0].value,
                    lon: inputs[1].value,
                    lonDir: selects[1].value
                });
            }
        });
    } else {
        const lat = document.getElementById('lat')?.value;
        const lon = document.getElementById('lon')?.value;
        if (lat && lon) {
            entry.data.coordinates = [{
                lat: lat,
                latDir: document.getElementById('latDir').value,
                lon: lon,
                lonDir: document.getElementById('lonDir').value
            }];
        }
    }
    
    // Handle additional fields
    ['rotation', 'size', 'radius', 'startAngle', 'endAngle'].forEach(field => {
        const element = document.getElementById(field);
        if (element) {
            entry.data[field] = parseFloat(element.value) || 0;
        }
    });
    
    entries.push(entry);
    renderEntries();
    updateEntryCount();
    
    // Reset form
    document.getElementById('entryType').value = '';
    updateEntryForm();
}

// Render all entries
function renderEntries() {
    const container = document.getElementById('entriesContainer');
    container.innerHTML = '';
    
    entries.forEach(entry => {
        const entryDiv = document.createElement('div');
        entryDiv.className = 'card entry-card';
        entryDiv.innerHTML = createEntryHTML(entry);
        container.appendChild(entryDiv);
    });
}

// Create HTML for a single entry
function createEntryHTML(entry) {
    let html = `
        <div class="card-header entry-header">
            <span class="entry-type">${entry.type}</span>
            <button class="btn btn-sm btn-outline-danger delete-btn" onclick="deleteEntry(${entry.id})">
                <i class="fas fa-trash"></i>
            </button>
        </div>
        <div class="card-body">
    `;
    
    // Display entry-specific data
    if (entry.data.instName) {
        html += `<p><strong>Instance Name:</strong> ${entry.data.instName}</p>`;
    }
    
    if (entry.data.text) {
        html += `<p><strong>Text:</strong> ${entry.data.text}</p>`;
    }
    
    if (entry.data.comment) {
        html += `<p><strong>Comment:</strong> ${entry.data.comment}</p>`;
    }
    
    // Display coordinates
    if (entry.data.coordinates && entry.data.coordinates.length > 0) {
        html += '<p><strong>Coordinates:</strong></p><ul>';
        entry.data.coordinates.forEach(coord => {
            html += `<li>${coord.lat},${coord.latDir} ${coord.lon},${coord.lonDir}</li>`;
        });
        html += '</ul>';
    }
    
    // Display additional parameters
    ['rotation', 'size', 'radius', 'startAngle', 'endAngle'].forEach(field => {
        if (entry.data[field] !== undefined) {
            const label = field.charAt(0).toUpperCase() + field.slice(1);
            html += `<p><strong>${label}:</strong> ${entry.data[field]}</p>`;
        }
    });
    
    html += '</div>';
    return html;
}

// Delete entry
function deleteEntry(id) {
    entries = entries.filter(entry => entry.id !== id);
    renderEntries();
    updateEntryCount();
}

// Update entry count
function updateEntryCount() {
    document.getElementById('entryCount').textContent = entries.length;
}

// Export data to file
function exportData() {
    // CSV Header
    let csvContent = 'Type,InstName,Text,Comment,Lat,LatDir,Lon,LonDir,Rotation,Size,Radius,StartAngle,EndAngle,AdditionalCoords\n';

    entries.forEach(entry => {
        csvContent += generateCSVRow(entry);
    });

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'umap_export.csv';
    a.click();
    URL.revokeObjectURL(url);
}

// Generate CSV row for an entry
function generateCSVRow(entry) {
    const escapeCSV = (str) => {
        if (!str) return '';
        // Escape quotes and wrap in quotes if contains comma, quote, or newline
        if (str.includes(',') || str.includes('"') || str.includes('\n')) {
            return '"' + str.replace(/"/g, '""') + '"';
        }
        return str;
    };

    let row = [];

    // Type
    row.push(entry.type);

    // InstName
    row.push(escapeCSV(entry.data.instName || ''));

    // Text
    row.push(escapeCSV(entry.data.text || ''));

    // Comment
    row.push(escapeCSV(entry.data.comment || ''));

    // Primary coordinates
    if (entry.data.coordinates && entry.data.coordinates[0]) {
        const coord = entry.data.coordinates[0];
        row.push(coord.lat || '');
        row.push(coord.latDir || '');
        row.push(coord.lon || '');
        row.push(coord.lonDir || '');
    } else {
        row.push('', '', '', '');
    }

    // Rotation
    row.push(entry.data.rotation !== undefined ? entry.data.rotation : '');

    // Size
    row.push(entry.data.size !== undefined ? entry.data.size : '');

    // Radius
    row.push(entry.data.radius !== undefined ? entry.data.radius : '');

    // Start Angle
    row.push(entry.data.startAngle !== undefined ? entry.data.startAngle : '');

    // End Angle
    row.push(entry.data.endAngle !== undefined ? entry.data.endAngle : '');

    // Additional coordinates (for polygons, lines, etc.)
    if (entry.data.coordinates && entry.data.coordinates.length > 1) {
        const additionalCoords = entry.data.coordinates.slice(1).map(coord =>
            `${coord.lat},${coord.latDir},${coord.lon},${coord.lonDir}`
        ).join(';');
        row.push(escapeCSV(additionalCoords));
    } else {
        row.push('');
    }

    return row.join(',') + '\n';
}

// Load sample data from the CSV file
function loadSampleData() {
    fetch('sample_umap.csv')
        .then(response => response.text())
        .then(content => {
            parseImportedData(content);
        })
        .catch(error => {
            console.error('Error loading sample data:', error);
            // Fallback to hardcoded sample data
            entries = [
                {
                    id: ++entryIdCounter,
                    type: 'SYMBOL',
                    data: {
                        instName: '~WARNSY6',
                        comment: 'Warning Comment',
                        coordinates: [{ lat: '17,48.000', latDir: 'N', lon: '055,56.000', lonDir: 'W' }]
                    }
                },
                {
                    id: ++entryIdCounter,
                    type: 'TEXT',
                    data: {
                        text: 'Warning Text',
                        comment: 'Warning Text Comment',
                        coordinates: [{ lat: '17,48.000', latDir: 'N', lon: '055,56.000', lonDir: 'W' }],
                        rotation: 0,
                        size: 12
                    }
                }
            ];

            renderEntries();
            updateEntryCount();
            alert('Loaded fallback sample data. To load your actual file, use the Import File feature.');
        });
}

// Import file
function importFile(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = function(e) {
        const content = e.target.result;
        parseImportedData(content);
    };
    reader.readAsText(file);
}

// Parse imported data
function parseImportedData(content) {
    entries = [];
    entryIdCounter = 0;

    // Check if it's CSV format
    if (content.includes('Type,InstName,Text,Comment') || content.split('\n')[0].includes(',')) {
        parseCSVData(content);
    } else {
        // Parse original text format
        parseTextData(content);
    }

    renderEntries();
    updateEntryCount();
    alert(`Successfully imported ${entries.length} entries!`);
}

// Parse CSV format data
function parseCSVData(content) {
    const lines = content.split('\n').map(line => line.trim()).filter(line => line);

    if (lines.length === 0) return;

    // Skip header if present
    let startIndex = 0;
    if (lines[0].includes('Type,InstName,Text,Comment')) {
        startIndex = 1;
    }

    for (let i = startIndex; i < lines.length; i++) {
        const row = parseCSVRow(lines[i]);
        if (row.length >= 4 && row[0]) { // Must have at least type
            const entry = {
                id: ++entryIdCounter,
                type: row[0],
                data: {
                    instName: row[1] || '',
                    text: row[2] || '',
                    comment: row[3] || '',
                    coordinates: []
                }
            };

            // Parse primary coordinates
            if (row[4] && row[6]) { // lat and lon
                entry.data.coordinates.push({
                    lat: row[4],
                    latDir: row[5] || 'N',
                    lon: row[6],
                    lonDir: row[7] || 'W'
                });
            }

            // Parse additional fields
            if (row[8]) entry.data.rotation = parseFloat(row[8]);
            if (row[9]) entry.data.size = parseFloat(row[9]);
            if (row[10]) entry.data.radius = parseFloat(row[10]);
            if (row[11]) entry.data.startAngle = parseFloat(row[11]);
            if (row[12]) entry.data.endAngle = parseFloat(row[12]);

            // Parse additional coordinates
            if (row[13]) {
                const additionalCoords = row[13].split(';');
                additionalCoords.forEach(coordStr => {
                    const parts = coordStr.split(',');
                    if (parts.length >= 4) {
                        entry.data.coordinates.push({
                            lat: parts[0],
                            latDir: parts[1],
                            lon: parts[2],
                            lonDir: parts[3]
                        });
                    }
                });
            }

            entries.push(entry);
        }
    }
}

// Parse CSV row handling quoted values
function parseCSVRow(line) {
    const result = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
        const char = line[i];

        if (char === '"') {
            if (inQuotes && line[i + 1] === '"') {
                // Escaped quote
                current += '"';
                i++; // Skip next quote
            } else {
                // Toggle quote state
                inQuotes = !inQuotes;
            }
        } else if (char === ',' && !inQuotes) {
            // End of field
            result.push(current);
            current = '';
        } else {
            current += char;
        }
    }

    // Add the last field
    result.push(current);

    return result;
}

// Parse original text format data
function parseTextData(content) {
    const lines = content.split('\n').map(line => line.trim());

    let i = 0;
    while (i < lines.length) {
        const line = lines[i];

        // Skip comments and empty lines
        if (line.startsWith('//') || line === '') {
            i++;
            continue;
        }

        // Parse different entry types
        if (line.startsWith('SYMBOL,')) {
            i = parseSymbolEntry(lines, i);
        } else if (line.startsWith('TEXT,')) {
            i = parseTextEntry(lines, i);
        } else if (line === 'POLYGON') {
            i = parsePolygonEntry(lines, i);
        } else if (line.startsWith('LINE,')) {
            i = parseLineEntry(lines, i);
        } else if (line === 'CIRCLE') {
            i = parseCircleEntry(lines, i);
        } else if (line === 'FAN') {
            i = parseFanEntry(lines, i);
        } else if (line === 'ARC') {
            i = parseArcEntry(lines, i);
        } else if (line === 'DANGER_LINE') {
            i = parseDangerLineEntry(lines, i);
        } else if (line === 'DANGER_AREA') {
            i = parseDangerAreaEntry(lines, i);
        } else {
            i++;
        }
    }
}

// Parse SYMBOL entry
function parseSymbolEntry(lines, startIndex) {
    const symbolLine = lines[startIndex];
    const parts = symbolLine.split(',');
    const instName = parts[1] || '***';

    const comment = lines[startIndex + 1] || '';
    const coordLine = lines[startIndex + 2] || '';

    const entry = {
        id: ++entryIdCounter,
        type: 'SYMBOL',
        data: {
            instName: instName,
            comment: comment,
            coordinates: parseCoordinateLine(coordLine)
        }
    };

    entries.push(entry);
    return startIndex + 3;
}

// Parse TEXT entry
function parseTextEntry(lines, startIndex) {
    const textLine = lines[startIndex];
    const parts = textLine.split(',');
    const text = parts[1] || '';

    const comment = lines[startIndex + 1] || '';
    const coordLine = lines[startIndex + 2] || '';
    const coordParts = coordLine.split(',');

    const entry = {
        id: ++entryIdCounter,
        type: 'TEXT',
        data: {
            text: text,
            comment: comment,
            coordinates: parseCoordinateLine(coordLine),
            rotation: parseInt(coordParts[4]) || 0,
            size: parseInt(coordParts[5]) || 12
        }
    };

    entries.push(entry);
    return startIndex + 3;
}

// Parse POLYGON entry
function parsePolygonEntry(lines, startIndex) {
    const comment = lines[startIndex + 1] || '';
    const coordinates = [];

    let i = startIndex + 2;
    while (i < lines.length && lines[i] !== 'END') {
        if (lines[i].trim() !== '') {
            const coords = parseCoordinateLine(lines[i]);
            if (coords.length > 0) {
                coordinates.push(coords[0]);
            }
        }
        i++;
    }

    const entry = {
        id: ++entryIdCounter,
        type: 'POLYGON',
        data: {
            comment: comment,
            coordinates: coordinates
        }
    };

    entries.push(entry);
    return i + 1; // Skip the END line
}

// Parse LINE entry
function parseLineEntry(lines, startIndex) {
    const lineLine = lines[startIndex];
    const parts = lineLine.split(',');
    const instName = parts[1] || '***';

    const comment = lines[startIndex + 1] || '';
    const coordinates = [];

    let i = startIndex + 2;
    while (i < lines.length && lines[i] !== 'END') {
        if (lines[i].trim() !== '') {
            const coords = parseCoordinateLine(lines[i]);
            if (coords.length > 0) {
                coordinates.push(coords[0]);
            }
        }
        i++;
    }

    const entry = {
        id: ++entryIdCounter,
        type: 'LINE',
        data: {
            instName: instName,
            comment: comment,
            coordinates: coordinates
        }
    };

    entries.push(entry);
    return i + 1; // Skip the END line
}

// Parse CIRCLE entry
function parseCircleEntry(lines, startIndex) {
    const comment = lines[startIndex + 1] || '';
    const coordLine = lines[startIndex + 2] || '';
    const parts = coordLine.split(',');

    const entry = {
        id: ++entryIdCounter,
        type: 'CIRCLE',
        data: {
            comment: comment,
            coordinates: parseCoordinateLine(coordLine),
            radius: parseFloat(parts[4]) || 1.0
        }
    };

    entries.push(entry);
    return startIndex + 3;
}

// Parse FAN entry
function parseFanEntry(lines, startIndex) {
    const comment = lines[startIndex + 1] || '';
    const coordLine = lines[startIndex + 2] || '';
    const parts = coordLine.split(',');

    const entry = {
        id: ++entryIdCounter,
        type: 'FAN',
        data: {
            comment: comment,
            coordinates: parseCoordinateLine(coordLine),
            radius: parseFloat(parts[4]) || 1.0,
            startAngle: parseFloat(parts[5]) || 0,
            endAngle: parseFloat(parts[6]) || 90
        }
    };

    entries.push(entry);
    return startIndex + 3;
}

// Parse ARC entry
function parseArcEntry(lines, startIndex) {
    const comment = lines[startIndex + 1] || '';
    const coordLine = lines[startIndex + 2] || '';
    const parts = coordLine.split(',');

    const entry = {
        id: ++entryIdCounter,
        type: 'ARC',
        data: {
            comment: comment,
            coordinates: parseCoordinateLine(coordLine),
            radius: parseFloat(parts[4]) || 1.0,
            startAngle: parseFloat(parts[5]) || 0,
            endAngle: parseFloat(parts[6]) || 90
        }
    };

    entries.push(entry);
    return startIndex + 3;
}

// Parse DANGER_LINE entry
function parseDangerLineEntry(lines, startIndex) {
    const comment = lines[startIndex + 1] || '';
    const coordinates = [];

    let i = startIndex + 2;
    while (i < lines.length && lines[i] !== 'END') {
        if (lines[i].trim() !== '') {
            const coords = parseCoordinateLine(lines[i]);
            if (coords.length > 0) {
                coordinates.push(coords[0]);
            }
        }
        i++;
    }

    const entry = {
        id: ++entryIdCounter,
        type: 'DANGER_LINE',
        data: {
            comment: comment,
            coordinates: coordinates
        }
    };

    entries.push(entry);
    return i + 1; // Skip the END line
}

// Parse DANGER_AREA entry
function parseDangerAreaEntry(lines, startIndex) {
    const comment = lines[startIndex + 1] || '';
    const coordinates = [];

    let i = startIndex + 2;
    while (i < lines.length && lines[i] !== 'END') {
        if (lines[i].trim() !== '') {
            const coords = parseCoordinateLine(lines[i]);
            if (coords.length > 0) {
                coordinates.push(coords[0]);
            }
        }
        i++;
    }

    const entry = {
        id: ++entryIdCounter,
        type: 'DANGER_AREA',
        data: {
            comment: comment,
            coordinates: coordinates
        }
    };

    entries.push(entry);
    return i + 1; // Skip the END line
}

// Parse coordinate line
function parseCoordinateLine(line) {
    if (!line || line.trim() === '') return [];

    const parts = line.split(',');
    if (parts.length >= 4) {
        return [{
            lat: parts[0] + ',' + parts[1],
            latDir: parts[2],
            lon: parts[3] + ',' + parts[4],
            lonDir: parts[5] || 'W'
        }];
    }
    return [];
}

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    updateEntryCount();
});
