# JRC ECDIS User Map Editor

A web-based editor for ECDIS (Electronic Chart Display and Information System) user map data with CSV support.

## Features

### Entry Types Supported
- **SYMBOL** - Navigation symbols with dropdown selection of symbol types:
  - Triangle (~TRIANG6)
  - Circle (~CIRCLE6)
  - Square (~SQUARE6)
  - Diamond (~DIAMND6)
  - X-Shape (~XSHAPE6)
  - Warning (~WARNSY6)
- **TEXT** - Text labels with rotation and size parameters
- **POLYGON** - Multi-point polygon areas
- **LINE** - Multi-point line features with instance names
- **CIRCLE** - Circular areas with radius
- **ELLIPSE** - Elliptical areas with horizontal and vertical radius
- **FAN** - Fan-shaped areas with radius and angle parameters
- **ARC** - Arc segments with radius and angle parameters
- **DANGER_LINE** - Warning line features
- **DANGER_AREA** - Warning area features

### Core Functionality
- **Add Entries**: Dropdown selection with dynamic forms for each entry type
- **Edit Entries**: Click "Edit" button to modify existing entries inline
- **Delete Entries**: Remove individual entries with confirmation
- **Clear All Data**: Remove all entries with confirmation
- **CSV Export**: Export all data to CSV format
- **CSV/TXT Import**: Import from both CSV and original ECDIS text formats
- **Sample Data**: Load example data to get started

### CSV Format
The application uses a structured CSV format with the following columns:
- Type, InstName, Text, Comment
- Lat, LatDir, Lon, LonDir (primary coordinates)
- Rotation, Size, Radius, StartAngle, EndAngle
- AdditionalCoords (for multi-point features like polygons and lines)

### Coordinate Handling
- **Single Point**: SYMBOL, TEXT, CIRCLE, FAN, ARC entries
- **Multi-Point**: POLYGON, LINE, DANGER_LINE, DANGER_AREA entries
- **Format**: Latitude/Longitude with direction indicators (N/S, E/W)
- **Dynamic Addition**: Add/remove coordinate points as needed

## Usage

1. **Start the Server**:
   ```bash
   python -m http.server 8000
   ```

2. **Open in Browser**:
   Navigate to `http://localhost:8000`

3. **Add Entries**:
   - Select entry type from dropdown
   - Fill in the dynamic form fields
   - Click "Add Entry"

4. **Edit Entries**:
   - Click "Edit" button on any entry
   - Modify fields in the inline form
   - Click "Save" to confirm or "Cancel" to discard

5. **Export Data**:
   - Click "Export to CSV" to download current data
   - File will be saved as `umap_export.csv`

6. **Import Data**:
   - Click "Choose File" and select a CSV or TXT file
   - Supports both CSV format and original ECDIS text format
   - Data will be automatically parsed and loaded

## File Structure
- `index.html` - Main application interface
- `app.js` - Application logic and data handling
- `sample_umap.csv` - Example CSV data file
- `umap_TEST.txt` - Original ECDIS text format example

## Technical Details
- **Frontend**: HTML5, CSS3, Bootstrap 5, Vanilla JavaScript
- **No Backend Required**: Runs entirely in the browser
- **File Handling**: Uses File API for import/export
- **Data Storage**: In-memory JavaScript arrays (session-based)

## Browser Compatibility
- Modern browsers with File API support
- Chrome, Firefox, Safari, Edge (recent versions)

## Notes
- Data is stored in browser memory only (not persistent)
- Export your work before closing the browser
- CSV format provides better compatibility with spreadsheet applications
- Original ECDIS text format is still supported for import
